package com.shuyun.toolhub.sdk.annotations;

import com.shuyun.toolhub.sdk.enums.AuthType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * LLM 工具提供者类注解
 *
 * <p>
 * Create on: 2025-05-28 / 星期三 / 上午 10:14
 * <AUTHOR>
 * @since v1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface KylinToolProvider {
    /**
     * 工具所属分类
     */
    String category() default "";

    /**
     * 基础服务域名
     */
    String baseUrl() default "";

    /**
     * 鉴权配置，具体内容根据 authType 决定
     */
    AuthConfig authConfig() default @AuthConfig;

    /**
     * 是否禁用该工具
     * <p>
     * 如果设置为 true，则该工具在 ToolHub 中不可用。
     */
    boolean disable() default false;
}