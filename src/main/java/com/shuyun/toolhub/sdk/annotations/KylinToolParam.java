package com.shuyun.toolhub.sdk.annotations;

import com.shuyun.toolhub.sdk.enums.ObjectType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * LLM 工具参数注解，用于注释 GET 方法的参数，或者 POST 方法的请求体参数。
 *
 * <p>
 * Create on: 2025-05-28 / 星期三 / 上午 10:14
 * <AUTHOR>
 * @since v1.0.0
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface KylinToolParam {

    /**
     * 参数类型，以 JsonSchema 的形式定义
     */
    ObjectType type();

    /**
     * 描述
     */
    String description();

    /**
     * 是否必填
     */
    boolean required() default false;

    /**
     * 示例值
     */
    String example() default "";
}