package com.shuyun.toolhub.sdk.utils;

import lombok.experimental.UtilityClass;

import java.util.function.Consumer;

/**
 * 通用工具
 *
 * <p>
 * Create on: 2025-05-29 / 星期四 / 下午 14:38
 * <AUTHOR>
 * @since v1.0.0
 */
@UtilityClass
public class Utils {
    public boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }

    public <T> void consumerIfNotNull(T t, Consumer<T> consumer) {
        if (isNotNull(t)) {
            consumer.accept(t);
        }
    }

    public boolean isNotNull(Object t) {
        if (t instanceof String) {
            return !isNullOrEmpty((String) t);
        }

        return t != null;
    }

    public <T> T nvl(T x, T defaultValue) {
        return isNotNull(x) ? x : defaultValue;
    }

    /**
     * 获取系统环境变量，如果未设置且 throwExWhenNull 为 true，则抛出异常
     */
    public String getSysEnvConf(String property, boolean throwExWhenNull) {
        String v = System.getProperty(property);
        if (Utils.isNullOrEmpty(v) && throwExWhenNull) {
            throw new IllegalStateException("环境变量未设置：" + property);
        }
        return v;
    }

    public String getSysEnvConf(String property, String dv) {
        return System.getProperty(property, dv);
    }
}
