package com.shuyun.toolhub.sdk.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.HttpMethod;
import com.shuyun.toolhub.sdk.model.JsonSchema;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 输入参数解析器
 *
 * <p>
 * Create on: 2025-05-28 / 星期三 / 下午 17:10
 * <AUTHOR>
 * @since v1.0.0
 */
@UtilityClass
public class InputSchemaParser {

    public String parse(HttpMethod httpMethod, Method method) {
        return Parser.getParser(httpMethod).parse(method);
    }

    public enum Parser {
        POST {
            @Override
            public String parse(Method method) {
                Class<?>[] parameterTypes = method.getParameterTypes();
                if (parameterTypes.length == 0) {
                    return "{}"; // 如果没有参数，返回空的 JSON 对象
                }

                Class<?> firstParamType = parameterTypes[0];
                String x = JsonSchemaUtils.generateSchema(firstParamType);
                x = processToolParamAnnotation(x, firstParamType);
                return x;
            }

            /**
             * 根据jsonText 中的 jsonPath，去改写 jsonSchemaText，主要修改两个字段：description和required
             */
            private String processToolParamAnnotation(String jsonSchemaText, Class<?> firstParamType) {
                Map<String, KylinToolParam> kylinToolParams = collectToolParamAnnotation(firstParamType);
                if (kylinToolParams.isEmpty()) {
                    return jsonSchemaText;
                }

                try {
                    // 使用 JsonSchema 对象解析
                    JsonSchema schema = JSONUtil.toBean(jsonSchemaText, JsonSchema.class);

                    // 收集所有需要设为 required 的字段
                    List<String> requiredFields = Utils.nvl(schema.getRequired(), new ArrayList<>());

                    // 处理每个注解
                    for (Map.Entry<String, KylinToolParam> entry : kylinToolParams.entrySet()) {
                        String jsonPath = entry.getKey();
                        KylinToolParam annotation = entry.getValue();

                        // 更新字段的 description 和 type
                        updateFieldInSchema(schema, jsonPath, annotation);

                        // 如果字段是必填的，添加到 required 列表
                        if (annotation.required()) {
                            // 对于嵌套字段，只添加顶层字段名到 required
                            String topLevelField = jsonPath.contains(".") ? jsonPath.split("\\.")[0] : jsonPath;
                            if (!requiredFields.contains(topLevelField)) {
                                requiredFields.add(topLevelField);
                            }
                        }
                    }

                    // 更新 required 字段
                    if (!requiredFields.isEmpty()) {
                        schema.setRequired(requiredFields);
                    }

                    return JSONUtil.toJsonStr(schema);

                } catch (Exception e) {
                    // 如果解析失败，返回原始文本
                    System.err.println("处理 JSON Schema 注解时出错: " + e.getMessage());
                    return jsonSchemaText;
                }
            }

            /**
             * 在 JsonSchema 对象中更新指定路径的字段信息
             */
            private void updateFieldInSchema(JsonSchema schema, String jsonPath, KylinToolParam annotation) {
                String[] pathParts = jsonPath.split("\\.");
                Map<String, JsonSchema.JsonSchemaProperty> currentProperties = schema.getProperties();

                if (currentProperties == null) {
                    return;
                }

                // 导航到目标字段
                JsonSchema.JsonSchemaProperty targetProperty = null;
                Map<String, JsonSchema.JsonSchemaProperty> parentProperties = currentProperties;

                for (int i = 0; i < pathParts.length; i++) {
                    String fieldName = pathParts[i];
                    JsonSchema.JsonSchemaProperty property = parentProperties.get(fieldName);

                    if (property == null) {
                        return; // 路径不存在
                    }

                    if (i == pathParts.length - 1) {
                        // 这是目标字段
                        targetProperty = property;
                    } else {
                        // 继续导航到下一级
                        parentProperties = property.getProperties();
                        if (parentProperties == null) {
                            return; // 路径不存在
                        }
                    }
                }

                // 更新目标字段的属性
                if (targetProperty != null) {
                    String description = annotation.description();
                    String example = annotation.example();
                    if (Utils.isNotNull(example)) {
                        description += "，示例：" + example;
                    }
                    targetProperty.setDescription(description);
                    targetProperty.setType(annotation.type().getType());
                }
            }

            /**
             * 返回 JsonPath + KylinToolParam 注解的 Map
             */
            private Map<String, KylinToolParam> collectToolParamAnnotation(Class<?> firstParamType) {
                Map<String, KylinToolParam> annotationMap = new HashMap<>();
                collectFieldAnnotations(firstParamType, "", annotationMap);
                return annotationMap;
            }

            /**
             * 递归收集字段上的 KylinToolParam 注解
             */
            private void collectFieldAnnotations(Class<?> clazz, String pathPrefix, Map<String, KylinToolParam> annotationMap) {
                if (clazz == null || clazz == Object.class) {
                    return;
                }

                // 获取当前类的所有声明字段（不包括继承的字段）
                Field[] fields = clazz.getDeclaredFields();

                for (Field field : fields) {
                    // 构建当前字段的 JsonPath
                    String fieldPath = pathPrefix.isEmpty() ? field.getName() : pathPrefix + "." + field.getName();

                    // 检查字段是否有 KylinToolParam 注解
                    KylinToolParam annotation = field.getAnnotation(KylinToolParam.class);
                    if (annotation != null) {
                        annotationMap.put(fieldPath, annotation);
                    }

                    // 如果字段类型是自定义类（非基本类型、包装类、String等），递归处理
                    Class<?> fieldType = field.getType();
                    if (shouldRecurseIntoType(fieldType)) {
                        collectFieldAnnotations(fieldType, fieldPath, annotationMap);
                    }
                }

                // 处理父类字段
                collectFieldAnnotations(clazz.getSuperclass(), pathPrefix, annotationMap);
            }

            /**
             * 判断是否需要递归处理该类型的字段
             */
            private boolean shouldRecurseIntoType(Class<?> type) {
                // 跳过基本类型
                if (type.isPrimitive()) {
                    return false;
                }

                // 跳过包装类型
                if (type == Boolean.class || type == Byte.class || type == Character.class ||
                        type == Short.class || type == Integer.class || type == Long.class ||
                        type == Float.class || type == Double.class) {
                    return false;
                }

                // 跳过常用类型
                if (type == String.class || type.isArray() || type.isEnum()) {
                    return false;
                }

                // 跳过 java.* 和 javax.* 包下的类
                String packageName = type.getPackage() != null ? type.getPackage().getName() : "";
                return !packageName.startsWith("java.") && !packageName.startsWith("javax.");
            }
        },
        GET {
            @Override
            public String parse(Method method) {
                Parameter[] params = method.getParameters();
                if (params == null || params.length == 0) {
                    return "{}"; // 如果没有参数，返回空的 JSON 对象
                }

                StringBuilder schemaBuilder = new StringBuilder("{ \"type\": \"object\", \"properties\": ");
                List<String> required = new ArrayList<>();
                processParams(params, required, schemaBuilder);
                processRequired(schemaBuilder, required);
                schemaBuilder.append(", \"additionalProperties\": false");
                schemaBuilder.append("}");
                return schemaBuilder.toString();
            }

            /**
             * 逐个处理 GET 参数
             */
            private void processParams(Parameter[] params, List<String> required, StringBuilder schemaBuilder) {
                schemaBuilder.append("{");
                for (int i = 0; i < params.length; i++) {
                    Parameter param = params[i];
                    String name = param.getName();
                    KylinToolParam paramAnnotation = param.getAnnotation(KylinToolParam.class);
                    String s = "\"" + name + "\":" + JsonSchemaUtils.generateSchema(param.getType());
                    if (paramAnnotation != null) {
                        // 自己组装 JsonSchema
                        JSONObject entries = new JSONObject();
                        entries.set("type", paramAnnotation.type().getType());
                        String description = paramAnnotation.description();
                        String example = paramAnnotation.example();
                        if (Utils.isNotNull(example)) {
                            description += "，示例：" + example;
                        }
                        entries.set("description", description);
                        s = String.format("\"%s\":%s", name, entries);

                        if (paramAnnotation.required()) {
                            required.add(name);
                        }
                    }
                    schemaBuilder.append(s);
                    if (i < params.length - 1) {
                        schemaBuilder.append(", ");
                    }
                }
                schemaBuilder.append("}");
            }

            /**
             * 处理 required 字段
             */
            private void processRequired(StringBuilder schemaBuilder, List<String> required) {
                if (required.isEmpty()) {
                    return;
                }

                schemaBuilder.append(", \"required\": [");
                for (int i = 0; i < required.size(); i++) {
                    schemaBuilder.append("\"").append(required.get(i)).append("\"");
                    if (i < required.size() - 1) {
                        schemaBuilder.append(", ");
                    }
                }
                schemaBuilder.append("]");
            }

        };

        public static Parser getParser(HttpMethod httpMethod) {
            return httpMethod == HttpMethod.GET ? GET : POST;
        }

        public abstract String parse(Method method);
    }
}
