package com.shuyun.toolhub.sdk.utils;

import cn.hutool.json.JSONUtil;
import com.shuyun.toolhub.sdk.annotations.AuthConfig;
import com.shuyun.toolhub.sdk.annotations.KylinTool;
import com.shuyun.toolhub.sdk.annotations.KylinToolProvider;
import com.shuyun.toolhub.sdk.enums.AuthType;
import com.shuyun.toolhub.sdk.model.JsonSchema;
import com.shuyun.toolhub.sdk.model.ToolDefinition;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Method;

/**
 * Method -> 工具转换器
 *
 * <p>
 * Create on: 2025-05-28 / 星期三 / 下午 16:32
 * <AUTHOR>
 * @since v1.0.0
 */
@UtilityClass
public final class ToolDefinitionConvertor {

    public static final Object CATEGORY_DEFAULT_VALUE;
    public static final Object DISABLE_DEFAULT_VALUE;
    public static final ToolDefinition.KylinEnv GLOBAL_KYLIN_ENV;

    static {
        try {
            CATEGORY_DEFAULT_VALUE = KylinTool.class.getDeclaredMethod("category").getDefaultValue();
            DISABLE_DEFAULT_VALUE = KylinTool.class.getDeclaredMethod("disable").getDefaultValue();

            String KYLIN_ENV = Utils.getSysEnvConf("toolhub.sdk.kylin.env", true);
            String KYLIN_SERVICE_NAME = Utils.getSysEnvConf("toolhub.sdk.kylin.service.name", true);
            String KYLIN_SERVICE_PORT = Utils.getSysEnvConf("toolhub.sdk.kylin.service.port", false);
            String KYLIN_API_VERSION = Utils.getSysEnvConf("toolhub.sdk.kylin.api.version", "v1");

            GLOBAL_KYLIN_ENV = new ToolDefinition.KylinEnv();
            GLOBAL_KYLIN_ENV.setKylinEnv(KYLIN_ENV);
            GLOBAL_KYLIN_ENV.setServiceName(KYLIN_SERVICE_NAME);
            GLOBAL_KYLIN_ENV.setServicePort(Integer.parseInt(KYLIN_SERVICE_PORT));
            GLOBAL_KYLIN_ENV.setApiVersion(KYLIN_API_VERSION);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException("Failed to initialize KylinTool method references", e);
        }
    }

    public static ToolDefinition methodConvertToToolDefinition(Method method) {
        KylinTool tool = method.getAnnotation(KylinTool.class);
        KylinToolProvider toolProvider = method.getClass().getAnnotation(KylinToolProvider.class);
        ToolDefinition toolDefinition = ToolDefinition.valueOf(toolProvider, tool);

        // 解析环境参数
        toolDefinition.setKylinEnv(GLOBAL_KYLIN_ENV);

        // 解析输入参数
        String inputSchema = InputSchemaParser.parse(tool.endpoint().httpMethod(), method);

        // 处理 kylin token
        inputSchema = processKylinToken(tool.authConfig(), inputSchema);

        toolDefinition.setInputSchema(inputSchema);

        // 尝试用类上的注解来覆盖掉属性（只覆盖默认值）
        applyProviderOverridesIfDefault(toolProvider, tool, toolDefinition);

        return toolDefinition;
    }

    static String processKylinToken(AuthConfig authConfig, String inputSchema) {
        if (authConfig.authType() == AuthType.KYLIN && !authConfig.anonymous()) {
            JsonSchema jsonSchema = JSONUtil.toBean(inputSchema, JsonSchema.class);
            jsonSchema.appendKylinToken();
            return JSONUtil.toJsonStr(jsonSchema);
        }
        return inputSchema;
    }

    private static void applyProviderOverridesIfDefault(KylinToolProvider toolProvider, KylinTool tool, ToolDefinition toolDefinition) {
        if (toolProvider == null) {
            return;
        }

        try {
            // 覆盖默认值，因为认证，理论上不应该有特殊性
            toolDefinition.setAuthConfig(ToolDefinition.AuthConfig.valueOf(toolProvider.authConfig()));

            // 尊重客户端设置
            if (isDefaultValue(tool.category(), CATEGORY_DEFAULT_VALUE)) {
                toolDefinition.setCategory(toolProvider.category());
            }
            if (isDefaultValue(tool.disable(), DISABLE_DEFAULT_VALUE)) {
                toolDefinition.setDisable(toolProvider.disable());
            }
        } catch (Exception ignored) {
        }
    }

    // 检查是否为默认值
    private static boolean isDefaultValue(Object value, Object defaultValue) {
        if (value == null && defaultValue == null) return true;
        return value != null && value.equals(defaultValue);
    }
}
