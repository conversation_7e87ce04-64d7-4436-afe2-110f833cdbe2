package com.shuyun.toolhub.sdk.model;

import com.shuyun.toolhub.sdk.annotations.Annotations;
import com.shuyun.toolhub.sdk.annotations.Endpoint;
import com.shuyun.toolhub.sdk.annotations.KylinTool;
import com.shuyun.toolhub.sdk.annotations.KylinToolProvider;
import com.shuyun.toolhub.sdk.enums.AuthType;
import com.shuyun.toolhub.sdk.enums.HttpMethod;
import com.shuyun.toolhub.sdk.enums.TokenLocation;
import com.shuyun.toolhub.sdk.utils.ToolDefinitionConvertor;
import com.shuyun.toolhub.sdk.utils.Utils;
import lombok.Data;

/**
 * 工具定义对象
 *
 * <p>
 * Create on: 2025-05-28 / 星期三 / 上午 10:47
 * <AUTHOR>
 * @since v1.0.0
 */
@Data
public class ToolDefinition {
    /**
     * 工具名称
     */
    private String name;

    /**
     * 工具描述
     */
    private String description;

    /**
     * 输入数据的 JSON Schema
     */
    private String inputSchema;

    /**
     * 工具所属分类
     */
    private String category;

    /**
     * 工具注解
     */
    private ToolAnnotations toolAnnotations;

    /**
     * 工具相关端点信息
     */
    private XEndpoint endpoint;

    /**
     * 麒麟环境配置
     */
    private KylinEnv kylinEnv;

    /**
     * 鉴权配置，具体内容根据 authType 决定
     */
    private AuthConfig authConfig;

    /**
     * 是否禁用该工具
     * <p>
     * 当设置为 true 时，该工具将不会被注册到工具中心，也不会被 LLM 调用
     */
    private boolean disable;

    /**
     * 从 KylinTool 注解创建 ToolDefinition 实例
     *
     * @param toolProvider
     * @param kylinTool    KylinTool 注解实例
     * @return 填充好值的 ToolDefinition 实例
     */
    public static ToolDefinition valueOf(KylinToolProvider toolProvider, KylinTool kylinTool) {
        ToolDefinition td = new ToolDefinition();
        td.setName(kylinTool.name());
        td.setDescription(kylinTool.description());
        td.setDisable(kylinTool.disable());
        td.setCategory(kylinTool.category());

        // 复杂属性
        td.setKylinEnv(ToolDefinitionConvertor.GLOBAL_KYLIN_ENV);
        td.setEndpoint(XEndpoint.valueOf(toolProvider, kylinTool.endpoint()));
        td.setToolAnnotations(ToolAnnotations.valueOf(kylinTool.annotations()));
        td.setAuthConfig(AuthConfig.valueOf(kylinTool.authConfig()));

        return td;
    }

    @Data
    public static class ToolAnnotations {
        // 工具颜色，用于在 UI 中标识工具
        private String color;
        // 是否只读操作
        private boolean readOnly;
        // 是否为破坏性操作
        private boolean destructive;
        // 是否为幂等操作
        private boolean idempotent;
        // 是否会连接外部资源
        private boolean openWorld;

        public static ToolAnnotations valueOf(Annotations annotations) {
            if (annotations == null) {
                return null;
            }
            ToolAnnotations toolAnnotations = new ToolAnnotations();
            toolAnnotations.setColor(annotations.color());
            toolAnnotations.setReadOnly(annotations.readOnly());
            toolAnnotations.setDestructive(annotations.destructive());
            toolAnnotations.setIdempotent(annotations.idempotent());
            toolAnnotations.setOpenWorld(annotations.openWorld());
            return toolAnnotations;
        }
    }

    @Data
    public static class XEndpoint {
        private String protocol;
        // 基础路径
        private String baseUrl;
        // 调用端点
        private String endpoint;
        // HTTP 请求方法
        private HttpMethod httpMethod;

        public static XEndpoint valueOf(KylinToolProvider toolProvider, Endpoint endpoint) {
            if (endpoint == null) {
                return null;
            }

            XEndpoint xEndpoint = new XEndpoint();
            xEndpoint.setProtocol(endpoint.protocol().getPrefix());

            // 域名优先级，方法注解 > 类注解 > 全局配置
            String baseUrl = endpoint.baseUrl();

            // 如果类上设置了，使用类注解
            if (Utils.isNullOrEmpty(baseUrl) && Utils.isNotNull(toolProvider.baseUrl())) {
                baseUrl = toolProvider.baseUrl();
            }

            // 如果全局设置了，则使用全局的
            String systemSetting = Utils.getSysEnvConf("toolhub.sdk.service.url", false);
            if (Utils.isNullOrEmpty(baseUrl) && Utils.isNotNull(systemSetting)) {
                baseUrl = systemSetting;
            }

            xEndpoint.setBaseUrl(baseUrl);
            xEndpoint.setEndpoint(endpoint.endpoint());
            xEndpoint.setHttpMethod(endpoint.httpMethod());
            return xEndpoint;
        }

        public String genRequestUrl() {
            // 如果 baseUrl 以斜杠结尾，且 endpoint 以斜杠开头，则去掉 endpoint 的斜杠
            if (baseUrl.endsWith("/") && endpoint.startsWith("/")) {
                return baseUrl + endpoint.substring(1);
            }

            // 添加协议前缀
            String requestUrl = baseUrl + endpoint;
            if (!requestUrl.toUpperCase().startsWith("HTTP")) {
                requestUrl = protocol + requestUrl;
            }

            return requestUrl;
        }
    }

    @Data
    public static class KylinEnv {
        // 麒麟环境
        private String kylinEnv;
        // 服务名称
        private String serviceName;
        // 服务端口
        private Integer servicePort;
        // API版本
        private String apiVersion;
    }

    @Data
    public static class AuthConfig {
        // 验证类型
        private AuthType authType;

        // ====================== 固定 Token ======================
        // 固定 Token 的 token
        private String token;
        // 固定 Token 的参数名称
        private String tokenParamName;
        // 固定 Token 的参数位置
        private TokenLocation tokenLocation;

        // ====================== OAuth2 ======================
        // OAuth2 的客户端ID
        private String appKey;
        // OAuth2 的客户端Secret
        private String secret;
        // OAuth2 的授权端点
        private String tokenEndpoint;
        // OAuth2 刷新令牌端点
        private String refreshTokenEndpoint;

        public static AuthConfig valueOf(com.shuyun.toolhub.sdk.annotations.AuthConfig authConfig) {
            if (authConfig == null) {
                return null;
            }

            AuthConfig config = new AuthConfig();
            config.setAuthType(authConfig.authType());

            Utils.consumerIfNotNull(authConfig.token(), config::setToken);
            Utils.consumerIfNotNull(authConfig.tokenParamName(), config::setTokenParamName);
            Utils.consumerIfNotNull(authConfig.tokenLocation(), config::setTokenLocation);

            Utils.consumerIfNotNull(authConfig.appKey(), config::setAppKey);
            Utils.consumerIfNotNull(authConfig.secret(), config::setSecret);
            Utils.consumerIfNotNull(authConfig.tokenEndpoint(), config::setTokenEndpoint);
            Utils.consumerIfNotNull(authConfig.refreshTokenEndpoint(), config::setRefreshTokenEndpoint);
            return config;
        }
    }
}