package com.shuyun.toolhub.sdk;

import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.HttpMethod;
import com.shuyun.toolhub.sdk.enums.ObjectType;
import com.shuyun.toolhub.sdk.utils.InputSchemaParser;

import java.lang.reflect.Method;

/**
 * InputSchemaParser 集成测试
 */
public class InputSchemaParserIntegrationTest {

    // 测试用的请求体类
    static class TestRequestBody {
        @KylinToolParam(type = ObjectType.STRING, description = "用户名", required = true)
        private String name;
        
        @KylinToolParam(type = ObjectType.INTEGER, description = "年龄")
        private Integer age;
        
        private String email; // 没有注解的字段
        
        @KylinToolParam(type = ObjectType.OBJECT, description = "用户地址")
        private Address address;
        
        // getter 和 setter
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public Address getAddress() { return address; }
        public void setAddress(Address address) { this.address = address; }
    }
    
    static class Address {
        @KylinToolParam(type = ObjectType.STRING, description = "城市", required = true)
        private String city;
        
        @KylinToolParam(type = ObjectType.STRING, description = "街道")
        private String street;
        
        private String zipCode; // 没有注解的字段
        
        // getter 和 setter
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        public String getStreet() { return street; }
        public void setStreet(String street) { this.street = street; }
        public String getZipCode() { return zipCode; }
        public void setZipCode(String zipCode) { this.zipCode = zipCode; }
    }
    
    // 测试控制器
    static class TestController {
        public void postMethodWithBody(TestRequestBody body) {
            // POST方法带请求体
        }
    }

    public static void main(String[] args) {
        System.out.println("开始 InputSchemaParser 集成测试...");
        
        try {
            testPostMethodWithAnnotations();
            System.out.println("\n🎉 集成测试通过！InputSchemaParser 完整流程工作正常！");
            
        } catch (Exception e) {
            System.err.println("❌ 集成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testPostMethodWithAnnotations() throws Exception {
        System.out.println("测试带注解的 POST 方法...");
        
        Method method = TestController.class.getMethod("postMethodWithBody", TestRequestBody.class);
        String schema = InputSchemaParser.parse(HttpMethod.POST, method);
        
        System.out.println("生成的完整 Schema:");
        System.out.println(schema);
        
        // 验证基本结构
        if (!schema.contains("\"type\"") || !schema.contains("\"properties\"")) {
            throw new AssertionError("Schema 缺少基本结构");
        }
        System.out.println("✅ Schema 基本结构正确");
        
        // 验证字段描述是否被正确设置
        if (schema.contains("\"description\" : \"用户名\"")) {
            System.out.println("✅ name 字段描述设置正确");
        } else {
            System.err.println("❌ name 字段描述设置失败");
        }
        
        if (schema.contains("\"description\" : \"年龄\"")) {
            System.out.println("✅ age 字段描述设置正确");
        } else {
            System.err.println("❌ age 字段描述设置失败");
        }
        
        if (schema.contains("\"description\" : \"用户地址\"")) {
            System.out.println("✅ address 字段描述设置正确");
        } else {
            System.err.println("❌ address 字段描述设置失败");
        }
        
        // 验证嵌套字段描述
        if (schema.contains("\"description\" : \"城市\"")) {
            System.out.println("✅ address.city 字段描述设置正确");
        } else {
            System.err.println("❌ address.city 字段描述设置失败");
        }
        
        if (schema.contains("\"description\" : \"街道\"")) {
            System.out.println("✅ address.street 字段描述设置正确");
        } else {
            System.err.println("❌ address.street 字段描述设置失败");
        }
        
        // 验证 required 字段
        if (schema.contains("\"required\"")) {
            System.out.println("✅ required 字段存在");
            
            if (schema.contains("\"name\"") && schema.contains("\"address\"")) {
                System.out.println("✅ required 字段包含正确的必填字段");
            } else {
                System.err.println("❌ required 字段内容不正确");
            }
        } else {
            System.err.println("❌ required 字段缺失");
        }
        
        // 验证类型是否被正确覆盖
        if (schema.contains("\"type\" : \"string\"") && 
            schema.contains("\"type\" : \"integer\"") && 
            schema.contains("\"type\" : \"object\"")) {
            System.out.println("✅ 字段类型设置正确");
        } else {
            System.err.println("❌ 字段类型设置可能有问题");
        }
        
        System.out.println("POST 方法注解处理测试完成");
    }
}
