package com.shuyun.toolhub.sdk.utils;

import com.shuyun.toolhub.sdk.annotations.*;
import com.shuyun.toolhub.sdk.enums.AuthType;
import com.shuyun.toolhub.sdk.enums.HttpMethod;
import com.shuyun.toolhub.sdk.enums.TokenLocation;
import com.shuyun.toolhub.sdk.model.ToolDefinition;

import java.lang.reflect.Method;

/**
 * ToolDefinitionConvertor 单元测试类
 *
 * <p>
 * Create on: 2025-01-27 / 星期一 / 下午 15:00
 * <AUTHOR>
 * @since v1.0.0
 */
public class ToolDefinitionConvertorTest {

    public static void main(String[] args) {
        System.out.println("开始测试 ToolDefinitionConvertor 类...\n");
        
        // 设置测试环境变量
        setupTestEnvironment();
        
        try {
            // 测试主要转换方法
            testMethodConvertToToolDefinition();

            // 测试带有类级别注解覆盖的方法转换
            testMethodConvertWithProviderOverride();

            // 测试 Kylin Token 处理
            testProcessKylinToken();

            // 测试默认值检查
            testIsDefaultValue();

            // 测试静态常量
            testStaticConstants();

            System.out.println("\n✅ 所有测试通过！");
            
        } catch (Exception e) {
            System.err.println("\n❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 设置测试环境变量
     */
    private static void setupTestEnvironment() {
        System.setProperty("toolhub.sdk.kylin.env", "test");
        System.setProperty("toolhub.sdk.kylin.service.name", "test-service");
        System.setProperty("toolhub.sdk.kylin.service.port", "8080");
        System.setProperty("toolhub.sdk.kylin.api.version", "v1");
    }

    /**
     * 测试 methodConvertToToolDefinition 方法
     */
    private static void testMethodConvertToToolDefinition() throws Exception {
        System.out.println("🧪 测试 methodConvertToToolDefinition 方法...");
        
        // 测试基本方法转换
        Method testMethod = TestToolClass.class.getMethod("testMethod", String.class);
        ToolDefinition result = ToolDefinitionConvertor.methodConvertToToolDefinition(testMethod);
        
        // 验证基本属性
        assertEquals(result.getName(), "testTool", "工具名称应该正确");
        assertEquals(result.getDescription(), "测试工具", "工具描述应该正确");
        assertEquals(result.getCategory(), "测试分类", "工具分类应该正确");
        assertFalse(result.isDisable(), "工具应该是启用状态");
        
        // 验证环境配置
        assertNotNull(result.getKylinEnv(), "Kylin环境配置不应为空");
        assertEquals(result.getKylinEnv().getKylinEnv(), "test", "Kylin环境应该正确");
        assertEquals(result.getKylinEnv().getServiceName(), "test-service", "服务名称应该正确");
        assertEquals(result.getKylinEnv().getServicePort(), Integer.valueOf(8080), "服务端口应该正确");
        
        // 验证输入Schema不为空
        assertNotNull(result.getInputSchema(), "输入Schema不应为空");
        
        System.out.println("✅ methodConvertToToolDefinition 测试通过");
    }

    /**
     * 测试带有类级别注解覆盖的方法转换
     */
    private static void testMethodConvertWithProviderOverride() throws Exception {
        System.out.println("🧪 测试带有类级别注解覆盖的方法转换...");
        
        Method testMethod = TestToolWithProviderClass.class.getMethod("testMethodWithDefaults");
        ToolDefinition result = ToolDefinitionConvertor.methodConvertToToolDefinition(testMethod);
        
        // 验证类级别注解覆盖了默认值
        assertEquals(result.getCategory(), "提供者分类", "应该使用类级别的分类");
        assertTrue(result.isDisable(), "应该使用类级别的禁用设置");
        
        System.out.println("✅ 类级别注解覆盖测试通过");
    }

    /**
     * 测试 processKylinToken 方法
     */
    private static void testProcessKylinToken() {
        System.out.println("🧪 测试 processKylinToken 方法...");
        
        // 创建测试用的认证配置
        AuthConfig kylinAuthConfig = createKylinAuthConfig();
        AuthConfig anonymousAuthConfig = createAnonymousAuthConfig();
        AuthConfig tokenAuthConfig = createTokenAuthConfig();
        
        // 测试空的输入Schema
        String emptySchema = "{}";
        String result1 = ToolDefinitionConvertor.processKylinToken(kylinAuthConfig, emptySchema);
        assertContains(result1, "___kylin_user_token___", "应包含kylin_user_token参数");
        
        // 测试已有属性的Schema
        String existingSchema = "{\"type\":\"object\",\"properties\":{\"city\":{\"type\":\"string\"}},\"required\":[\"city\"]}";
        String result2 = ToolDefinitionConvertor.processKylinToken(kylinAuthConfig, existingSchema);
        assertContains(result2, "\"city\"", "应保留原有属性");
        assertContains(result2, "___kylin_user_token___", "应包含kylin_user_token参数");
        
        // 测试匿名访问
        String result3 = ToolDefinitionConvertor.processKylinToken(anonymousAuthConfig, emptySchema);
        assertEquals(result3, emptySchema, "匿名访问不应修改Schema");
        
        // 测试其他认证类型
        String result4 = ToolDefinitionConvertor.processKylinToken(tokenAuthConfig, emptySchema);
        assertEquals(result4, emptySchema, "TOKEN认证不应修改Schema");
        
        System.out.println("✅ processKylinToken 测试通过");
    }

    /**
     * 测试 isDefaultValue 方法
     */
    private static void testIsDefaultValue() throws Exception {
        System.out.println("🧪 测试 isDefaultValue 方法...");
        
        // 使用反射访问私有方法
        Method isDefaultValueMethod = ToolDefinitionConvertor.class.getDeclaredMethod("isDefaultValue", Object.class, Object.class);
        isDefaultValueMethod.setAccessible(true);
        
        // 测试相等的值
        Boolean result1 = (Boolean) isDefaultValueMethod.invoke(null, "test", "test");
        assertTrue(result1, "相等的字符串应该返回true");
        
        // 测试不相等的值
        Boolean result2 = (Boolean) isDefaultValueMethod.invoke(null, "test1", "test2");
        assertFalse(result2, "不相等的字符串应该返回false");
        
        // 测试null值
        Boolean result3 = (Boolean) isDefaultValueMethod.invoke(null, null, null);
        assertTrue(result3, "两个null值应该返回true");
        
        Boolean result4 = (Boolean) isDefaultValueMethod.invoke(null, "test", null);
        assertFalse(result4, "一个null一个非null应该返回false");
        
        System.out.println("✅ isDefaultValue 测试通过");
    }

    /**
     * 测试静态常量
     */
    private static void testStaticConstants() {
        System.out.println("🧪 测试静态常量...");
        
        // 验证静态常量不为空
        assertNotNull(ToolDefinitionConvertor.CATEGORY_DEFAULT_VALUE, "CATEGORY_DEFAULT_VALUE不应为空");
        assertNotNull(ToolDefinitionConvertor.DISABLE_DEFAULT_VALUE, "DISABLE_DEFAULT_VALUE不应为空");
        assertNotNull(ToolDefinitionConvertor.GLOBAL_KYLIN_ENV, "GLOBAL_KYLIN_ENV不应为空");
        
        // 验证环境配置
        assertEquals(ToolDefinitionConvertor.GLOBAL_KYLIN_ENV.getKylinEnv(), "test", "Kylin环境应该正确");
        assertEquals(ToolDefinitionConvertor.GLOBAL_KYLIN_ENV.getServiceName(), "test-service", "服务名称应该正确");
        
        System.out.println("✅ 静态常量测试通过");
    }

    // ==================== 辅助方法 ====================

    private static AuthConfig createKylinAuthConfig() {
        return new AuthConfig() {
            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return AuthConfig.class;
            }

            @Override
            public AuthType authType() {
                return AuthType.KYLIN;
            }

            @Override
            public boolean anonymous() {
                return false;
            }

            @Override
            public String token() {
                return "test-token";
            }

            @Override
            public String tokenParamName() {
                return "token";
            }

            @Override
            public TokenLocation tokenLocation() {
                return TokenLocation.QUERY;
            }

            @Override
            public String appKey() {
                return "";
            }

            @Override
            public String secret() {
                return "";
            }

            @Override
            public String tokenEndpoint() {
                return "";
            }

            @Override
            public String refreshTokenEndpoint() {
                return "";
            }
        };
    }

    private static AuthConfig createAnonymousAuthConfig() {
        return new AuthConfig() {
            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return AuthConfig.class;
            }

            @Override
            public AuthType authType() {
                return AuthType.KYLIN;
            }

            @Override
            public boolean anonymous() {
                return true;
            }

            @Override
            public String token() {
                return "";
            }

            @Override
            public String tokenParamName() {
                return "";
            }

            @Override
            public TokenLocation tokenLocation() {
                return TokenLocation.QUERY;
            }

            @Override
            public String appKey() {
                return "";
            }

            @Override
            public String secret() {
                return "";
            }

            @Override
            public String tokenEndpoint() {
                return "";
            }

            @Override
            public String refreshTokenEndpoint() {
                return "";
            }
        };
    }

    private static AuthConfig createTokenAuthConfig() {
        return new AuthConfig() {
            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return AuthConfig.class;
            }

            @Override
            public AuthType authType() {
                return AuthType.TOKEN;
            }

            @Override
            public boolean anonymous() {
                return false;
            }

            @Override
            public String token() {
                return "fixed-token";
            }

            @Override
            public String tokenParamName() {
                return "api_key";
            }

            @Override
            public TokenLocation tokenLocation() {
                return TokenLocation.QUERY;
            }

            @Override
            public String appKey() {
                return "";
            }

            @Override
            public String secret() {
                return "";
            }

            @Override
            public String tokenEndpoint() {
                return "";
            }

            @Override
            public String refreshTokenEndpoint() {
                return "";
            }
        };
    }

    // ==================== 断言方法 ====================

    private static void assertEquals(Object actual, Object expected, String message) {
        if (actual == null && expected == null) {
            return;
        }
        if (actual == null || !actual.equals(expected)) {
            throw new AssertionError(message + "，期望值: " + expected + "，实际值: " + actual);
        }
    }

    private static void assertNotNull(Object actual, String message) {
        if (actual == null) {
            throw new AssertionError(message);
        }
    }

    private static void assertTrue(boolean condition, String message) {
        if (!condition) {
            throw new AssertionError(message);
        }
    }

    private static void assertFalse(boolean condition, String message) {
        if (condition) {
            throw new AssertionError(message);
        }
    }

    private static void assertContains(String actual, String expected, String message) {
        if (actual == null || !actual.contains(expected)) {
            throw new AssertionError(message + "，期望包含: " + expected + "，实际值: " + actual);
        }
    }

    // ==================== 测试用的类和方法 ====================

    /**
     * 测试工具类
     */
    @KylinToolProvider(
            category = "提供者分类",
            baseUrl = "http://test.api",
            disable = true
    )
    public static class TestToolClass {

        @KylinTool(
                name = "testTool",
                description = "测试工具",
                category = "测试分类",
                endpoint = @Endpoint(
                        baseUrl = "http://api.test.com",
                        endpoint = "/test",
                        httpMethod = HttpMethod.GET
                ),
                authConfig = @AuthConfig(
                        authType = AuthType.KYLIN,
                        anonymous = false
                ),
                disable = false
        )
        public String testMethod(String param) {
            return "test";
        }
    }

    /**
     * 带有默认值的测试工具类
     */
    @KylinToolProvider(
            category = "提供者分类",
            disable = true
    )
    public static class TestToolWithProviderClass {

        @KylinTool(
                name = "testToolWithDefaults",
                description = "带默认值的测试工具"
                // category 和 disable 使用默认值，应该被类级别注解覆盖
        )
        public String testMethodWithDefaults() {
            return "test";
        }
    }
}
