package com.shuyun.toolhub.sdk;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.HttpMethod;
import com.shuyun.toolhub.sdk.enums.ObjectType;
import com.shuyun.toolhub.sdk.utils.InputSchemaParser;
import lombok.Data;

import java.lang.reflect.Method;

/**
 * 简单的集成测试
 */
public class SimpleIntegrationTest {

    // 简单的测试类
    @Data
    static class SimpleUser {
        @KylinToolParam(type = ObjectType.STRING, description = "用户姓名", required = true)
        private String name;
        
        @KylinToolParam(type = ObjectType.INTEGER, description = "用户年龄")
        private Integer age;

        @JsonPropertyDescription("用户邮箱")
        @JsonProperty(required = true)
        private String email; // 没有注解
    }

    static class TestController {
        public void createUser(SimpleUser user) {
            // 创建用户
        }
    }

    public static void main(String[] args) {
        System.out.println("开始简单集成测试...");
        
        try {
            Method method = TestController.class.getMethod("createUser", SimpleUser.class);
            String schema = InputSchemaParser.parse(HttpMethod.POST, method);
            
            System.out.println("生成的 Schema:");
            System.out.println(schema);
            
            // 简单验证
            boolean hasNameDescription = schema.contains("用户姓名");
            boolean hasAgeDescription = schema.contains("用户年龄");
            boolean hasRequired = schema.contains("required") && schema.contains("name");
            
            if (hasNameDescription && hasAgeDescription && hasRequired) {
                System.out.println("\n✅ 测试通过！注解处理正常工作！");
                System.out.println("- ✅ name 字段描述正确");
                System.out.println("- ✅ age 字段描述正确");
                System.out.println("- ✅ required 字段设置正确");
            } else {
                System.err.println("\n❌ 测试失败！");
                if (!hasNameDescription) System.err.println("- ❌ name 字段描述缺失");
                if (!hasAgeDescription) System.err.println("- ❌ age 字段描述缺失");
                if (!hasRequired) System.err.println("- ❌ required 字段设置错误");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
