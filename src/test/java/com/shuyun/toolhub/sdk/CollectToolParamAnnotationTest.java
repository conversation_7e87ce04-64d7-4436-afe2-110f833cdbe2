package com.shuyun.toolhub.sdk;

import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.ObjectType;
import com.shuyun.toolhub.sdk.utils.InputSchemaParser;
import lombok.Data;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 测试 collectToolParamAnnotation 方法
 */
public class CollectToolParamAnnotationTest {

    // 测试用的嵌套类
    @Data
    static class TestUser {
        @KylinToolParam(type = ObjectType.STRING, description = "用户名", required = true)
        private String name;
        
        @KylinToolParam(type = ObjectType.INTEGER, description = "年龄")
        private Integer age;
        
        private String email; // 没有注解的字段
        
        @KylinToolParam(type = ObjectType.OBJECT, description = "用户地址")
        private Address address;

        @KylinToolParam(type = ObjectType.NUMBER, description = "余额", required = true)
        private BigDecimal money; // 没有注解的字段
    }

    @Data
    static class Address {
        @KylinToolParam(type = ObjectType.STRING, description = "城市", required = true)
        private String city;
        
        @KylinToolParam(type = ObjectType.STRING, description = "街道")
        private String street;
        
        private String zipCode; // 没有注解的字段
    }

    public static void main(String[] args) {
        System.out.println("开始测试 collectToolParamAnnotation 方法...");
        
        try {
            // 使用反射调用私有方法
            Method collectMethod = InputSchemaParser.class.getDeclaredMethod("collectToolParamAnnotation", Class.class);
            collectMethod.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Map<String, KylinToolParam> result = (Map<String, KylinToolParam>) collectMethod.invoke(null, TestUser.class);
            
            System.out.println("\n收集到的注解映射:");
            for (Map.Entry<String, KylinToolParam> entry : result.entrySet()) {
                KylinToolParam annotation = entry.getValue();
                System.out.printf("  %s -> type: %s, description: %s, required: %s%n", 
                    entry.getKey(), 
                    annotation.type().getType(), 
                    annotation.description(), 
                    annotation.required());
            }
            
            // 验证结果
            System.out.println("\n开始验证结果...");
            
            if (result.size() != 6) {
                System.err.println("❌ 期望收集到5个注解，但实际收集到: " + result.size());
                return;
            }
            System.out.println("✅ 注解数量正确: " + result.size());
            
            // 验证顶层字段
            if (checkAnnotation(result, "name", ObjectType.STRING, "用户名", true) &&
                checkAnnotation(result, "age", ObjectType.INTEGER, "年龄", false) &&
                checkAnnotation(result, "address", ObjectType.OBJECT, "用户地址", false) &&
                checkAnnotation(result, "address.city", ObjectType.STRING, "城市", true) &&
                checkAnnotation(result, "address.street", ObjectType.STRING, "街道", false)) {
                
                System.out.println("✅ 所有注解验证通过");
            }
            
            // 验证没有注解的字段不会被收集
            if (result.containsKey("email") || result.containsKey("address.zipCode")) {
                System.err.println("❌ 不应该收集没有注解的字段");
                return;
            }
            System.out.println("✅ 正确忽略了没有注解的字段");
            
            System.out.println("\n🎉 所有测试通过！collectToolParamAnnotation 方法实现正确！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static boolean checkAnnotation(Map<String, KylinToolParam> result, String path, 
                                         ObjectType expectedType, String expectedDescription, 
                                         boolean expectedRequired) {
        if (!result.containsKey(path)) {
            System.err.println("❌ 缺少路径: " + path);
            return false;
        }
        
        KylinToolParam annotation = result.get(path);
        if (annotation.type() != expectedType) {
            System.err.println("❌ 路径 " + path + " 的类型不匹配，期望: " + expectedType + "，实际: " + annotation.type());
            return false;
        }
        
        if (!annotation.description().equals(expectedDescription)) {
            System.err.println("❌ 路径 " + path + " 的描述不匹配，期望: " + expectedDescription + "，实际: " + annotation.description());
            return false;
        }
        
        if (annotation.required() != expectedRequired) {
            System.err.println("❌ 路径 " + path + " 的required不匹配，期望: " + expectedRequired + "，实际: " + annotation.required());
            return false;
        }
        
        System.out.println("✅ 路径 " + path + " 验证通过");
        return true;
    }
}
