# Kylin MCP SDK

## 项目简介 

Kylin MCP SDK 是一个用于快速开发和注册工具到 ToolHub 平台的 Java SDK。它基于 JDK 8，使用 shade 打包，提供了一套简单易用的注解和工具类，帮助开发者定义、注册和管理工具。

## 功能特性

- 通过注解快速定义工具
- 自动生成工具的 JSON Schema
- 支持多种 HTTP 方法和认证方式
- 自动扫描和注册工具到 ToolHub 平台

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.shuyun.toolhub</groupId>
    <artifactId>kylin-mcp-sdk</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 创建工具类

**工具注册注解，完整示例：**
```java
@KylinToolProvider(category = "数据查询工具")
public class DataQueryTools {

    @KylinTool(
            name = "getUserInfo",
            description = "获取用户信息",
            endpoint = @Endpoint(
                    baseUrl = "https://api.example.com",
                    endpoint = "/users",
                    httpMethod = HttpMethod.GET
            ),
            annotations = @Annotations(
                    color = "#CCC",
                    readOnly = true,
                    destructive = false,
                    idempotent = true,
                    openWorld = true
            ),
            authConfig = @AuthConfig(
                    authType = AuthType.KYLIN,
                    anonymous = false,
                    token = "kylin-api-key",
                    tokenParamName = "key",
                    tokenLocation = TokenLocation.QUERY,
                    secret = "kylin-secret",
                    tokenEndpoint = "http://kylin.api/token",
                    refreshTokenEndpoint = "http://kylin.api/refresh-token"
            ),
            disable = false
    )
    public String getUserInfo(@KylinToolParam(description = "用户ID", required = true, type = ObjectType.INTEGER) Long userId) {
        // 实现获取用户信息的逻辑
        return "User info for " + userId;
    }
}
```

**工具注册注解，最简示例：（大多数情况使用此即可）**
```java
public class DataQueryTools {

    @KylinTool(
            name = "getUserInfo",
            description = "获取用户信息",
            endpoint = @Endpoint(
                    endpoint = "/users",
                    httpMethod = HttpMethod.GET
            )
    )
    public String getUserInfo(@KylinToolParam(description = "用户ID", required = true, type = ObjectType.INTEGER) Long userId) {
        // 实现获取用户信息的逻辑
        return "User info for " + userId;
    }
}
```

注解： **KylinToolParam** 可以用于 GET 方法的参数，或者 POST 方法的请求体参数，如：
```java
getUserInfo(@KylinToolParam(description = "用户ID", required = true, type = ObjectType.INTEGER) Long userId);

// ====================== 或者 POST 参数对象 ======================

@Data
static class Request {
    @KylinToolParam(type = ObjectType.STRING, description = "用户姓名", required = true)
    private String name;

    @KylinToolParam(type = ObjectType.INTEGER, description = "用户年龄")
    private Integer age;
}
```

若项目中使用了 Jackson，对于 POST 的参数对象，还可以使用 **Jackson 注解** 来定义请求体参数，如：
```java
import lombok.Data;

// 测试请求体类
@Data
static class TestRequestBody {
    @JsonProperty(required = true)
    @JsonPropertyDescription("用户姓名")
    private String name;

    @JsonPropertyDescription("年龄")
    private int age;
}
```

### 3. 客户端初始化 SDK 并注册工具

```java
public class Application {
    public static void main(String[] args) {
        // 初始化 SDK 并扫描指定包下的工具
        ToolHubSDK.initialize("com.example.tools");
    }
}
```

## 注解说明

### @KylinToolProvider

用于标记工具提供者类，可以设置类级别的默认属性。

| 属性 | 说明 | 默认值 |
| --- | --- | --- |
| category | 工具分类 | "" |
| authType | 认证类型 | AuthType.KYLIN |
| authConfig | 认证配置 | @AuthConfig |
| disable | 是否禁用 | false |

### @KylinTool

用于标记工具方法。

| 属性 | 说明 | 默认值 |
| --- | --- | --- |
| name | 工具名称 | 必填 |
| description | 工具描述 | 必填 |
| category | 工具分类 | "" |
| endpoint | 端点配置 | @Endpoint |
| annotations | 工具注解 | @Annotations |
| authConfig | 认证配置 | @AuthConfig |
| disable | 是否禁用 | false |

### @KylinToolParam

用于标记工具方法参数。

| 属性 | 说明 | 默认值 |
| --- | --- | --- |
| description | 参数描述 | "" |
| required | 是否必填 | false |
| type | 参数类型 | ObjectType.STRING |

## 环境配置

SDK 支持通过系统属性配置环境参数：

```
-Dtoolhub.sdk.kylin.env=xtty
-Dtoolhub.sdk.kylin.service.name=eboss
-Dtoolhub.sdk.kylin.service.port=8080
-Dtoolhub.sdk.kylin.api.version=v1
-Dtoolhub.sdk.server.url=http://toolhub.example.com/api/tools/register
```